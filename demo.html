<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chrome插件流式输出演示</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      padding: 30px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }
    
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 30px;
      font-size: 2.5em;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .demo-section {
      margin: 30px 0;
      padding: 20px;
      border: 2px solid #e8eaed;
      border-radius: 8px;
      background: #fafbfc;
    }
    
    .demo-section h2 {
      color: #5f6368;
      margin-top: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .demo-text {
      background: white;
      padding: 15px;
      border-radius: 6px;
      border: 1px solid #dadce0;
      margin: 15px 0;
      font-size: 14px;
      line-height: 1.6;
    }
    
    .highlight {
      background: #fff3cd;
      padding: 2px 4px;
      border-radius: 3px;
      border: 1px solid #ffeaa7;
    }
    
    .instructions {
      background: #e8f0fe;
      border: 1px solid #1a73e8;
      border-radius: 6px;
      padding: 15px;
      margin: 20px 0;
    }
    
    .instructions h3 {
      margin-top: 0;
      color: #1a73e8;
    }
    
    .step {
      margin: 10px 0;
      padding-left: 20px;
      position: relative;
    }
    
    .step::before {
      content: counter(step-counter);
      counter-increment: step-counter;
      position: absolute;
      left: 0;
      top: 0;
      background: #1a73e8;
      color: white;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
    }
    
    .instructions ol {
      counter-reset: step-counter;
      list-style: none;
      padding: 0;
    }
    
    .feature-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin: 20px 0;
    }
    
    .feature-item {
      background: white;
      padding: 15px;
      border-radius: 6px;
      border: 1px solid #e8eaed;
      transition: all 0.3s ease;
    }
    
    .feature-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .feature-item h4 {
      margin-top: 0;
      color: #5f6368;
    }
    
    .badge {
      display: inline-block;
      background: #34a853;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: bold;
      margin-left: 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🚀 Chrome插件流式输出演示</h1>
    
    <div class="instructions">
      <h3>📋 使用说明</h3>
      <ol>
        <li class="step">选中下面任意一段文字</li>
        <li class="step">点击Chrome插件图标打开侧边栏</li>
        <li class="step">点击"🏷️ 智能标签"或"🌐 中英对照"按钮</li>
        <li class="step">观察流式输出效果！</li>
      </ol>
    </div>
    
    <div class="demo-section">
      <h2>🏷️ 智能标签生成演示 <span class="badge">NEW</span></h2>
      <div class="demo-text">
        人工智能技术正在快速发展，深度学习和机器学习算法在各个领域都有广泛应用。从自然语言处理到计算机视觉，从推荐系统到自动驾驶，AI技术正在改变我们的生活方式。随着大语言模型的兴起，ChatGPT、GPT-4等模型展现了强大的文本生成和理解能力，为人机交互带来了新的可能性。
      </div>
      <p><strong>预期效果：</strong>AI将逐字生成相关标签，如 <span class="highlight">#人工智能 #深度学习 #机器学习 #自然语言处理 #大语言模型</span></p>
    </div>
    
    <div class="demo-section">
      <h2>🌐 中英对照翻译演示 <span class="badge">NEW</span></h2>
      <div class="demo-text">
        Artificial Intelligence is revolutionizing industries across the globe. Machine learning algorithms can now process vast amounts of data to identify patterns and make predictions with unprecedented accuracy. The development of large language models has opened new frontiers in natural language understanding and generation.
      </div>
      <p><strong>预期效果：</strong>AI将逐字生成中文翻译，展现流畅的打字机效果</p>
    </div>
    
    <div class="demo-section">
      <h2>📚 中文内容翻译演示</h2>
      <div class="demo-text">
        量子计算是一种基于量子力学原理的计算方式，它利用量子比特的叠加态和纠缠特性来进行信息处理。与传统计算机使用的二进制比特不同，量子比特可以同时处于0和1的叠加态，这使得量子计算机在处理某些特定问题时具有指数级的速度优势。
      </div>
      <p><strong>预期效果：</strong>AI将逐字生成英文翻译</p>
    </div>
    
    <div class="demo-section">
      <h2>✨ 新功能特性</h2>
      <div class="feature-list">
        <div class="feature-item">
          <h4>⚡ 流式输出</h4>
          <p>打字机效果，逐字显示AI生成的内容，提升用户体验</p>
        </div>
        <div class="feature-item">
          <h4>🎮 用户控制</h4>
          <p>支持暂停、继续、停止和跳过动画，完全由用户控制</p>
        </div>
        <div class="feature-item">
          <h4>🎨 视觉优化</h4>
          <p>改进的UI界面，更好的动画效果和状态指示</p>
        </div>
        <div class="feature-item">
          <h4>🛡️ 错误处理</h4>
          <p>完善的错误处理机制，支持内容恢复和重试</p>
        </div>
        <div class="feature-item">
          <h4>📱 响应式设计</h4>
          <p>适配不同屏幕尺寸，在各种设备上都有良好体验</p>
        </div>
        <div class="feature-item">
          <h4>⚙️ 智能配置</h4>
          <p>可调节输出速度，自定义动画效果和用户偏好</p>
        </div>
      </div>
    </div>
    
    <div class="demo-section">
      <h2>🔧 技术实现</h2>
      <div class="demo-text">
        <strong>核心技术：</strong>
        <ul>
          <li><strong>流式文本渲染器：</strong>实现打字机效果的核心逻辑</li>
          <li><strong>UI控制器：</strong>管理用户交互和界面状态</li>
          <li><strong>错误处理：</strong>完善的异常处理和恢复机制</li>
          <li><strong>API集成：</strong>支持多种AI服务提供商的流式API</li>
          <li><strong>性能优化：</strong>高效的文本处理和内存管理</li>
        </ul>
      </div>
    </div>
  </div>
</body>
</html>

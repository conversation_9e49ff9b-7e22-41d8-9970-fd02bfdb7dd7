// 简化的侧边栏脚本
document.addEventListener('DOMContentLoaded', async () => {
  const contentText = document.getElementById('content-text');
  const contentInfo = document.getElementById('content-info');
  const pageTitle = document.getElementById('page-title');
  const pageUrl = document.getElementById('page-url');
  const statusMessage = document.getElementById('status-message');
  const saveButton = document.getElementById('save-to-flomo');
  const generateTagsBtn = document.getElementById('generate-tags');
  const translateBtn = document.getElementById('translate-text');

  // AI功能互斥状态管理
  let currentAIOperation = null;

  // 流式输出控制器
  let streamingController = null;

  // 初始化流式输出功能
  async function initializeStreaming() {
    try {
      const { StreamingUIController } = await import('./src/streaming/streaming-ui-controller.js');
      streamingController = new StreamingUIController({
        showControls: true,
        allowSkip: true,
        showProgress: true
      });

      // 为textarea创建控制容器
      const controlsParent = contentText.parentElement;
      streamingController.initialize(contentText, controlsParent);

      console.log('✅ 流式输出功能已初始化');
    } catch (error) {
      console.error('❌ 流式输出功能初始化失败:', error);
      // 降级到非流式模式
      streamingController = null;
    }
  }

  // 显示状态消息
  function showStatus(message, type) {
    statusMessage.textContent = message;
    statusMessage.className = `status ${type}`;
    statusMessage.style.display = 'block';

    if (type === 'success') {
      setTimeout(() => {
        statusMessage.style.display = 'none';
      }, 3000);
    }
  }

  // 加载待处理的内容
  async function loadPendingContent() {
    try {
      const result = await chrome.storage.local.get('pendingContent');
      if (result.pendingContent) {
        const { selectedText, pageTitle: title, pageUrl: url } = result.pendingContent;

        if (selectedText) {
          contentText.value = selectedText;

          if (title && url) {
            pageTitle.textContent = title;
            pageUrl.textContent = url;
            contentInfo.style.display = 'block';
          }
        }

        // 清理临时数据
        await chrome.storage.local.remove('pendingContent');
      }
    } catch (error) {
      console.error('加载内容失败:', error);
    }
  }

  // 保存到Flomo
  async function saveToFlomo() {
    const content = contentText.value.trim();

    if (!content) {
      showStatus('请输入要保存的内容', 'error');
      return;
    }

    saveButton.disabled = true;
    saveButton.classList.add('loading');
    saveButton.textContent = '保存中...';

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'saveToFlomo',
        content: content
      });

      if (response.success) {
        showStatus('内容已成功保存到 Flomo！', 'success');
        contentText.value = '';
        contentInfo.style.display = 'none';

        // 检查侧边栏配置，决定是否自动关闭
        try {
          const result = await chrome.storage.sync.get('sidepanelConfig');
          const sidepanelConfig = {
            autoClose: true, // 默认保存后自动关闭
            autoCloseDelay: 1000, // 1秒后关闭
            ...(result.sidepanelConfig || {})
          };

          if (sidepanelConfig.autoClose) {
            const delay = sidepanelConfig.autoCloseDelay || 1000;
            setTimeout(() => {
              window.close();
            }, delay);
          }
        } catch (error) {
          console.error('获取侧边栏配置失败:', error);
          // 默认行为：1秒后关闭
          setTimeout(() => {
            window.close();
          }, 1000);
        }
      } else {
        showStatus(response.error || '保存失败', 'error');
      }
    } catch (error) {
      showStatus('保存失败: ' + error.message, 'error');
    } finally {
      saveButton.disabled = false;
      saveButton.classList.remove('loading');
      saveButton.textContent = '保存到 Flomo';
    }
  }

  // 智能标签生成 - 支持流式输出
  async function generateTags() {
    const content = contentText.value.trim();

    if (!content) {
      showStatus('请先输入内容', 'error');
      return;
    }

    // 检查是否有其他AI操作正在进行
    if (currentAIOperation) {
      showStatus(`请等待 ${currentAIOperation} 完成后再执行其他AI功能`, 'error');
      return;
    }

    // 设置当前操作状态
    currentAIOperation = '标签生成';

    generateTagsBtn.disabled = true;
    generateTagsBtn.classList.add('loading');

    try {
      // 保存原始内容
      const originalContent = content;

      // 准备显示区域
      contentText.value = originalContent + '\n\n🏷️ 标签：';

      if (streamingController) {
        // 使用流式输出
        await generateTagsStreaming(originalContent);
      } else {
        // 降级到传统方式
        const tags = await simpleTagGeneration(originalContent);
        contentText.value = originalContent + '\n\n🏷️ 标签：' + tags;
        showStatus('标签已生成', 'success');
      }
    } catch (error) {
      console.error('标签生成失败:', error);
      showStatus('标签生成失败: ' + error.message, 'error');
    } finally {
      generateTagsBtn.disabled = false;
      generateTagsBtn.classList.remove('loading');
      // 清除当前操作状态
      currentAIOperation = null;
    }
  }

  // 流式标签生成
  async function generateTagsStreaming(content) {
    // 创建一个临时的textarea用于流式输出
    const streamingArea = document.createElement('textarea');
    streamingArea.style.cssText = contentText.style.cssText;
    streamingArea.className = contentText.className;

    // 替换原始textarea
    const parent = contentText.parentElement;
    const originalTextarea = contentText;
    parent.replaceChild(streamingArea, contentText);

    // 更新全局引用
    const tempContentText = contentText;
    window.contentText = streamingArea; // 临时更新全局引用

    try {
      await streamingController.startStreaming('tags', async (callbacks) => {
        // 调用AI服务的流式标签生成
        await callStreamingTagGeneration(content, callbacks);
      }, {
        speed: 80, // 标签生成稍快一些
        enableCursor: true
      });

      // 流式输出完成后，将结果合并到原始内容
      const generatedTags = streamingArea.value;
      originalTextarea.value = content + '\n\n🏷️ 标签：' + generatedTags;

    } finally {
      // 恢复原始textarea
      parent.replaceChild(originalTextarea, streamingArea);
      window.contentText = originalTextarea; // 恢复全局引用
    }
  }

  // 调用流式标签生成API
  async function callStreamingTagGeneration(content, callbacks) {
    const { onChunk, onComplete, onError, signal } = callbacks;

    try {
      // 确保AI配置已初始化
      if (!AI_CONFIG) {
        await initializeAIConfig();
      }

      // 检查AI配置是否有效
      if (!AI_CONFIG || !AI_CONFIG.apiUrl || !AI_CONFIG.apiKey) {
        throw new Error('AI配置无效：缺少API地址或密钥');
      }

      // 获取标签生成Prompt模板
      let prompt;
      try {
        const { legacyAdapter } = await import('./src/config/adapters/legacy-adapter.js');
        prompt = legacyAdapter.getTagGenerationPrompt(content, 'structured');
      } catch (error) {
        console.warn('⚠️ 无法加载Prompt模板，使用默认模板:', error);
        // 降级到默认模板
        prompt = `请为以下内容提取3-6个合适的标签，用中文回答，每个标签用空格分隔，不要包含#符号：\n\n${content}`;
      }

      // 调用流式API
      const response = await fetch(AI_CONFIG.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AI_CONFIG.apiKey}`
        },
        body: JSON.stringify({
          model: AI_CONFIG.model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: AI_CONFIG.temperature,
          max_tokens: AI_CONFIG.max_tokens,
          stream: true
        }),
        signal
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorData.error?.message || '未知错误'}`);
      }

      // 处理流式响应
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let accumulatedContent = '';

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim();

              if (data === '[DONE]') {
                onComplete({ tags: accumulatedContent.split(/[,，\s]+/).filter(tag => tag.trim()) });
                return;
              }

              try {
                const parsed = JSON.parse(data);
                const content = parsed.choices?.[0]?.delta?.content || '';

                if (content) {
                  accumulatedContent += content;
                  onChunk(content);
                }
              } catch (e) {
                // 忽略解析错误
                console.warn('解析流式响应失败:', e);
              }
            }
          }
        }

        // 如果没有收到[DONE]信号，手动完成
        onComplete({ tags: accumulatedContent.split(/[,，\s]+/).filter(tag => tag.trim()) });

      } finally {
        reader.releaseLock();
      }

    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('标签生成已取消');
      } else {
        console.error('流式标签生成失败:', error);
        onError(error);
      }
    }
  }

  // 智能翻译功能 - 支持流式输出
  async function translateText() {
    const content = contentText.value.trim();

    if (!content) {
      showStatus('请先输入内容', 'error');
      return;
    }

    // 检查是否有其他AI操作正在进行
    if (currentAIOperation) {
      showStatus(`请等待 ${currentAIOperation} 完成后再执行其他AI功能`, 'error');
      return;
    }

    // 设置当前操作状态
    currentAIOperation = '翻译';

    translateBtn.disabled = true;
    translateBtn.classList.add('loading');

    try {
      // 保存原始内容
      const originalContent = content;

      // 检测语言
      const isChineseText = /[\u4e00-\u9fff]/.test(content);
      const targetLang = isChineseText ? 'English' : '中文';

      // 准备显示区域 - 中英对照格式
      contentText.value = originalContent + '\n\n---\n\n🌐 ' + targetLang + '翻译：';

      if (streamingController) {
        // 使用流式输出
        await translateTextStreaming(originalContent, isChineseText);
      } else {
        // 降级到传统方式
        const translatedText = await simpleTranslation(originalContent, isChineseText);
        contentText.value = originalContent + '\n\n---\n\n🌐 ' + targetLang + '翻译：' + translatedText;
        showStatus('翻译已完成', 'success');
      }
    } catch (error) {
      console.error('翻译失败:', error);
      showStatus('翻译失败: ' + error.message, 'error');
    } finally {
      translateBtn.disabled = false;
      translateBtn.classList.remove('loading');
      // 清除当前操作状态
      currentAIOperation = null;
    }
  }

  // 流式翻译
  async function translateTextStreaming(content, isChinese) {
    // 创建一个临时的textarea用于流式输出
    const streamingArea = document.createElement('textarea');
    streamingArea.style.cssText = contentText.style.cssText;
    streamingArea.className = contentText.className;

    // 替换原始textarea
    const parent = contentText.parentElement;
    const originalTextarea = contentText;
    parent.replaceChild(streamingArea, contentText);

    // 更新全局引用
    window.contentText = streamingArea; // 临时更新全局引用

    try {
      await streamingController.startStreaming('translation', async (callbacks) => {
        // 调用AI服务的流式翻译
        await callStreamingTranslation(content, isChinese, callbacks);
      }, {
        speed: 60, // 翻译稍慢一些，便于阅读
        enableCursor: true
      });

      // 流式输出完成后，将结果合并到原始内容
      const translatedText = streamingArea.value;
      const targetLang = isChinese ? 'English' : '中文';
      originalTextarea.value = content + '\n\n---\n\n🌐 ' + targetLang + '翻译：' + translatedText;

    } finally {
      // 恢复原始textarea
      parent.replaceChild(originalTextarea, streamingArea);
      window.contentText = originalTextarea; // 恢复全局引用
    }
  }

  // 调用流式翻译API
  async function callStreamingTranslation(content, isChinese, callbacks) {
    const { onChunk, onComplete, onError, signal } = callbacks;

    try {
      // 确保AI配置已初始化
      if (!AI_CONFIG) {
        await initializeAIConfig();
      }

      // 检查AI配置是否有效
      if (!AI_CONFIG || !AI_CONFIG.apiUrl || !AI_CONFIG.apiKey) {
        throw new Error('AI配置无效：缺少API地址或密钥');
      }

      // 获取翻译Prompt模板
      let prompt;
      try {
        const { legacyAdapter } = await import('./src/config/adapters/legacy-adapter.js');
        const targetLang = isChinese ? 'English' : '中文';
        prompt = legacyAdapter.getTranslationPrompt(content, targetLang);
      } catch (error) {
        console.warn('⚠️ 无法加载Prompt模板，使用默认模板:', error);
        // 降级到默认模板
        const targetLang = isChinese ? 'English' : '中文';
        prompt = `请将以下内容翻译成${targetLang}，保持原意，只返回翻译结果：\n\n${content}`;
      }

      // 调用流式API
      const response = await fetch(AI_CONFIG.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AI_CONFIG.apiKey}`
        },
        body: JSON.stringify({
          model: AI_CONFIG.model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: AI_CONFIG.temperature,
          max_tokens: AI_CONFIG.max_tokens,
          stream: true
        }),
        signal
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorData.error?.message || '未知错误'}`);
      }

      // 处理流式响应
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let accumulatedContent = '';

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim();

              if (data === '[DONE]') {
                onComplete({
                  translation: {
                    original: content,
                    translated: accumulatedContent,
                    originalLanguage: isChinese ? 'zh' : 'en',
                    targetLanguage: isChinese ? 'en' : 'zh'
                  }
                });
                return;
              }

              try {
                const parsed = JSON.parse(data);
                const content = parsed.choices?.[0]?.delta?.content || '';

                if (content) {
                  accumulatedContent += content;
                  onChunk(content);
                }
              } catch (e) {
                // 忽略解析错误
                console.warn('解析流式响应失败:', e);
              }
            }
          }
        }

        // 如果没有收到[DONE]信号，手动完成
        onComplete({
          translation: {
            original: content,
            translated: accumulatedContent,
            originalLanguage: isChinese ? 'zh' : 'en',
            targetLanguage: isChinese ? 'en' : 'zh'
          }
        });

      } finally {
        reader.releaseLock();
      }

    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('翻译已取消');
      } else {
        console.error('流式翻译失败:', error);
        onError(error);
      }
    }
  }

  // 动态AI配置（从统一配置管理器获取）
  let AI_CONFIG = null;

  // 初始化AI配置
  async function initializeAIConfig() {
    try {
      // 导入配置适配器
      const { legacyAdapter } = await import('./src/config/adapters/legacy-adapter.js');
      AI_CONFIG = await legacyAdapter.getAIConfig();

      // 验证配置的有效性
      if (!AI_CONFIG || !AI_CONFIG.apiUrl || !AI_CONFIG.apiKey) {
        throw new Error('AI配置无效：缺少必要的API地址或密钥');
      }

      console.log('✅ AI配置已从统一配置管理器加载');
    } catch (error) {
      console.error('❌ 无法加载AI配置:', error);
      // 不再使用硬编码的默认配置，直接抛出错误
      AI_CONFIG = null;
      throw new Error(`AI配置加载失败: ${error.message}`);
    }
  }

  // 使用硅基流动AI生成标签
  async function simpleTagGeneration(text) {
    try {
      // 确保AI配置已初始化
      if (!AI_CONFIG) {
        await initializeAIConfig();
      }

      // 检查AI配置是否有效
      if (!AI_CONFIG || !AI_CONFIG.apiUrl || !AI_CONFIG.apiKey) {
        throw new Error('AI配置无效：缺少API地址或密钥');
      }

      // 获取标签生成Prompt模板
      let prompt;
      try {
        const { legacyAdapter } = await import('./src/config/adapters/legacy-adapter.js');
        prompt = legacyAdapter.getTagGenerationPrompt(text, 'structured');
      } catch (error) {
        console.warn('⚠️ 无法加载Prompt模板，使用默认模板:', error);
        // 降级到默认模板
        prompt = `你是一个顶级的知识管理和标签系统专家。你的任务是分析给定的文本，并生成一个包含相关标签的 JSON 数组。

严格遵守以下规则：
1.  **内容分析**：深入理解文本，提取核心概念、关键实体和主题。
2.  **层级优先**：尽可能创建有意义的层级标签，格式为 "父标签/子标签"。
3.  **简洁准确**：标签必须简明扼要，并与原文内容紧密相关。
4.  **语言一致**：标签的语言应与输入文本的语言保持一致。
5.  **数量控制**：生成 3 到 5 个标签。
6.  **格式要求**：必须返回一个格式正确的 JSON 字符串数组 (`["tag1", "tag2", "tag3"]`)，不包含任何其他解释。

### 示例 ###
输入文本: "OpenAI 宣布其最新的旗舰模型 GPT-4o 现已通过 API 向所有开发者开放。该模型在处理多模态输入方面表现出色，并且响应速度更快、成本更低。"
输出: ["技术/AI/大语言模型", "公司/OpenAI", "产品/GPT-4o", "技术/多模态"]

### 待处理 ###
输入文本: """
${text}
"""
输出:`;
      }
      const response = await callSiliconFlowAPI(prompt);

      // if (response) {
      //   const tags = response.split('，').map(tag => tag.trim()).filter(tag => tag);
      //   return '#' + tags.slice(0, 6).join(' #');
      // } else {
      //   throw new Error('AI响应为空');
      // }
      if (!response || response.trim() === '') {
        throw new Error('AI响应为空');
      }

      return response.trim();
    } catch (error) {
      console.error('AI标签生成失败:', error);
      // 不再使用本地关键词提取作为降级，直接抛出错误
      throw new Error(`标签生成失败: ${error.message}`);
    }
  }

  // 使用统一配置的AI翻译
  async function simpleTranslation(text, isChinese) {
    try {
      // 确保AI配置已初始化
      if (!AI_CONFIG) {
        await initializeAIConfig();
      }

      // 检查AI配置是否有效
      if (!AI_CONFIG || !AI_CONFIG.apiUrl || !AI_CONFIG.apiKey) {
        throw new Error('AI配置无效：缺少API地址或密钥');
      }

      // 获取翻译Prompt模板
      let prompt;
      try {
        const { legacyAdapter } = await import('./src/config/adapters/legacy-adapter.js');
        prompt = legacyAdapter.getTranslationPrompt(text);
      } catch (error) {
        console.warn('⚠️ 无法加载Prompt模板，使用默认模板:', error);
        // 降级到默认模板
        const targetLang = isChinese ? '英文' : '中文';
        prompt = `请将以下内容翻译成${targetLang}，保持原意，只返回翻译结果：\n\n${text}`;
      }

      const response = await callSiliconFlowAPI(prompt);

      if (!response || response.trim() === '') {
        throw new Error('AI响应为空');
      }

      return response.trim();
    } catch (error) {
      console.error('AI翻译失败:', error);
      // 不再使用占位符作为降级，直接抛出错误
      throw new Error(`翻译失败: ${error.message}`);
    }
  }

  // 调用硅基流动API
  async function callSiliconFlowAPI(prompt) {
    try {
      const response = await fetch(AI_CONFIG.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AI_CONFIG.apiKey}`
        },
        body: JSON.stringify({
          model: AI_CONFIG.model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: AI_CONFIG.temperature,
          max_tokens: AI_CONFIG.max_tokens,
          stream: false
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorData.error?.message || '未知错误'}`);
      }

      const data = await response.json();

      if (data.choices && data.choices.length > 0) {
        return data.choices[0].message.content;
      } else {
        throw new Error('API响应格式错误');
      }
    } catch (error) {
      console.error('硅基流动API调用失败:', error);
      throw error;
    }
  }



  // 事件监听
  saveButton.addEventListener('click', saveToFlomo);
  generateTagsBtn.addEventListener('click', generateTags);
  translateBtn.addEventListener('click', translateText);

  // 初始化
  await initializeAIConfig();
  await initializeStreaming();
  await loadPendingContent();
});

<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title>Flomo 保存工具</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f8f9fa;
      font-size: 14px;
      line-height: 1.5;
    }

    .container {
      max-width: 100%;
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    h1 {
      margin: 0 0 20px 0;
      font-size: 20px;
      color: #333;
      text-align: center;
    }

    .content-area {
      margin-bottom: 20px;
    }

    .content-info {
      background: #e8f0fe;
      border: 1px solid #1a73e8;
      border-radius: 4px;
      padding: 12px;
      margin-bottom: 16px;
      font-size: 13px;
      color: #1a73e8;
    }

    textarea {
      width: 100%;
      min-height: 200px;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      font-family: inherit;
      resize: vertical;
      box-sizing: border-box;
    }

    textarea:focus {
      outline: none;
      border-color: #1a73e8;
      box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
    }

    .ai-tools {
      display: flex;
      gap: 8px;
      margin: 16px 0;
      flex-wrap: wrap;
    }

    .ai-btn {
      padding: 8px 16px;
      background: #f1f3f4;
      border: 1px solid #dadce0;
      border-radius: 4px;
      cursor: pointer;
      font-size: 13px;
      color: #5f6368;
      transition: all 0.2s;
    }

    .ai-btn:hover {
      background: #e8eaed;
      border-color: #5f6368;
    }

    .ai-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .save-btn {
      width: 100%;
      padding: 12px;
      background: #1a73e8;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      margin-top: 16px;
    }

    .save-btn:hover {
      background: #1557b0;
    }

    .save-btn:disabled {
      background: #dadce0;
      cursor: not-allowed;
    }

    .status {
      margin-top: 12px;
      padding: 10px;
      border-radius: 4px;
      text-align: center;
      font-size: 13px;
      display: none;
    }

    .status.success {
      background: #e6f4ea;
      color: #137333;
      border: 1px solid #34a853;
    }

    .status.error {
      background: #fce8e6;
      color: #d93025;
      border: 1px solid #ea4335;
    }

    .loading {
      opacity: 0.7;
      pointer-events: none;
    }

    .loading::after {
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid #ccc;
      border-top: 2px solid #1a73e8;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 8px;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    /* 流式输出相关样式 */
    .streaming-controls {
      display: none;
      margin-top: 8px;
      gap: 8px;
      align-items: center;
      flex-wrap: wrap;
    }

    .streaming-btn {
      padding: 4px 8px;
      font-size: 12px;
      border: 1px solid #dadce0;
      border-radius: 4px;
      background: #f8f9fa;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #5f6368;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .streaming-btn:hover {
      background: #e8eaed;
      border-color: #5f6368;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .streaming-btn:active {
      transform: translateY(0);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .streaming-progress {
      display: none;
      margin-top: 4px;
      font-size: 12px;
      color: #666;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 4px;
      border: 1px solid #e8eaed;
    }

    .streaming-spinner {
      width: 12px;
      height: 12px;
      border: 2px solid #e8eaed;
      border-top: 2px solid #1a73e8;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .progress-text {
      font-weight: 500;
    }

    /* 改进的AI按钮样式 */
    .ai-btn {
      padding: 10px 16px;
      background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
      border: 1px solid #dadce0;
      border-radius: 6px;
      cursor: pointer;
      font-size: 13px;
      font-weight: 500;
      color: #5f6368;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .ai-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.5s ease;
    }

    .ai-btn:hover {
      background: linear-gradient(135deg, #e8eaed 0%, #dadce0 100%);
      border-color: #5f6368;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .ai-btn:hover::before {
      left: 100%;
    }

    .ai-btn:active {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .ai-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .ai-btn:disabled::before {
      display: none;
    }

    /* 改进的加载动画 */
    .loading {
      opacity: 0.8;
      pointer-events: none;
      position: relative;
    }

    .loading::after {
      content: '';
      display: inline-block;
      width: 14px;
      height: 14px;
      border: 2px solid #dadce0;
      border-top: 2px solid #1a73e8;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 8px;
      vertical-align: middle;
    }

    /* 改进的textarea样式 */
    textarea {
      width: 100%;
      min-height: 200px;
      padding: 16px;
      border: 2px solid #e8eaed;
      border-radius: 8px;
      font-size: 14px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      resize: vertical;
      box-sizing: border-box;
      transition: all 0.3s ease;
      background: #fafbfc;
    }

    textarea:focus {
      outline: none;
      border-color: #1a73e8;
      box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
      background: #ffffff;
    }

    /* 流式输出时的特殊样式 */
    textarea.streaming {
      border-color: #34a853;
      box-shadow: 0 0 0 3px rgba(52, 168, 83, 0.1);
      background: #f8fff9;
    }

    /* 完成状态的样式 */
    .status.success {
      background: linear-gradient(135deg, #e6f4ea 0%, #d4edda 100%);
      color: #137333;
      border: 1px solid #34a853;
      animation: slideIn 0.3s ease;
    }

    .status.error {
      background: linear-gradient(135deg, #fce8e6 0%, #f8d7da 100%);
      color: #d93025;
      border: 1px solid #ea4335;
      animation: slideIn 0.3s ease;
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* 响应式设计 */
    @media (max-width: 400px) {
      .ai-tools {
        flex-direction: column;
      }

      .ai-btn {
        width: 100%;
        justify-content: center;
      }

      .streaming-controls {
        justify-content: center;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>📝 Flomo 保存工具</h1>

    <div class="content-area">
      <div id="content-info" class="content-info" style="display: none;">
        来源：<span id="page-title"></span><br>
        链接：<span id="page-url"></span>
      </div>

      <textarea id="content-text" placeholder="选择页面文字后，内容会自动显示在这里..."></textarea>
    </div>

    <div class="ai-tools">
      <button class="ai-btn" id="generate-tags">🏷️ 智能标签</button>
      <button class="ai-btn" id="translate-text">🌐 中英对照</button>
    </div>

    <button class="save-btn" id="save-to-flomo">保存到 Flomo</button>

    <div id="status-message" class="status"></div>
  </div>

  <script src="sidepanel.js"></script>
</body>

</html>
// 统一配置模式定义
// 定义所有配置项的结构、默认值和验证规则

export const CONFIG_SCHEMA = {
  // AI服务配置
  ai: {
    // 默认配置
    defaults: {
      provider: 'deepseek',
      model: 'deepseek-chat',
      temperature: 0.7,
      maxTokens: 1000,
      timeout: 60000
    },

    // 支持的AI服务提供商配置
    providers: {
      siliconflow: {
        name: '硅基流动',
        baseUrl: 'https://api.siliconflow.cn/v1',
        apiKey: 'sk-bpbqulqtsmbywglsemzqantxqhmilksogyeitgcpkbvwioix',
        models: [
          'Qwen/Qwen2.5-7B-Instruct',
          'THUDM/glm-4-9b-chat',
          'Qwen/Qwen3-8B'
        ],
        keyFormat: /^sk-[a-zA-Z0-9]{48}$/
      },
      openrouter: {
        name: 'OpenRouter',
        baseUrl: 'https://openrouter.ai/api/v1',
        apiKey: '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',
        models: [
          'deepseek/deepseek-chat-v3-0324:free',
          'moonshotai/kimi-k2:free'
        ],
        keyFormat: /^sk-or-[a-zA-Z0-9-]+$/
      },
      deepseek: {
        name: 'DeepSeek',
        baseUrl: 'https://api.deepseek.com/v1',
        apiKey: '***********************************',
        models: [
          'deepseek-chat',
          'deepseek-coder'
        ],
        keyFormat: /^sk-[a-zA-Z0-9]{48}$/
      },
      moonshot: {
        name: 'Moonshot AI',
        baseUrl: 'https://api.moonshot.cn/v1',
        apiKey: 'sk-47X4c9L5qi1YZgdxmmX3klqIExMIgrhOZYuyAHpBgyz3OY65',
        models: [
          'moonshot-v1-8k',
          'moonshot-v1-32k',
          'moonshot-v1-128k'
        ],
        keyFormat: /^sk-[a-zA-Z0-9]{48}$/
      }
    },

    // 验证规则
    validation: {
      provider: (value) => {
        const validProviders = Object.keys(CONFIG_SCHEMA.ai.providers);
        return validProviders.includes(value);
      },
      model: (value, provider) => {
        if (!provider) return false;
        const providerConfig = CONFIG_SCHEMA.ai.providers[provider];
        return providerConfig && providerConfig.models.includes(value);
      },
      apiKey: (value, provider) => {
        if (!value || !provider) return false;
        const providerConfig = CONFIG_SCHEMA.ai.providers[provider];
        return providerConfig && providerConfig.keyFormat.test(value);
      },
      temperature: (value) => {
        return typeof value === 'number' && value >= 0 && value <= 1;
      },
      maxTokens: (value) => {
        return typeof value === 'number' && value > 0 && value <= 10000;
      }
    }
  },

  // Flomo配置
  flomo: {
    defaults: {
      apiUrl: '',
      timeout: 15000
    },

    validation: {
      apiUrl: (value) => {
        if (!value) return false;
        try {
          const url = new URL(value);
          return url.protocol === 'https:' && url.hostname.includes('flomo');
        } catch {
          return false;
        }
      }
    }
  },

  // 侧边栏配置
  sidepanel: {
    defaults: {
      autoClose: true,
      autoCloseDelay: 1000,
      theme: 'light',
      fontSize: 'medium'
    },

    validation: {
      autoClose: (value) => typeof value === 'boolean',
      autoCloseDelay: (value) => {
        return typeof value === 'number' && value >= 0 && value <= 10000;
      },
      theme: (value) => ['light', 'dark', 'auto'].includes(value),
      fontSize: (value) => ['small', 'medium', 'large'].includes(value)
    }
  },

  // UI配置
  ui: {
    defaults: {
      language: 'zh-CN',
      showTooltips: true,
      animationEnabled: true
    },

    validation: {
      language: (value) => ['zh-CN', 'en-US'].includes(value),
      showTooltips: (value) => typeof value === 'boolean',
      animationEnabled: (value) => typeof value === 'boolean'
    }
  }
};

// 配置优先级定义
export const CONFIG_PRIORITY = {
  // 优先级从高到低：environment > user > defaults
  ENVIRONMENT: 3,  // 环境变量配置（最高优先级）
  USER: 2,         // 用户设置
  DEFAULTS: 1      // 默认配置（最低优先级）
};

// 配置存储键名映射
export const STORAGE_KEYS = {
  ai: 'aiConfig',
  flomo: 'flomoConfig',
  sidepanel: 'sidepanelConfig',
  ui: 'uiConfig'
};

// 配置验证工具函数
export class ConfigValidator {
  static validate(section, key, value, context = {}) {
    const schema = CONFIG_SCHEMA[section];
    if (!schema || !schema.validation || !schema.validation[key]) {
      return { valid: true };
    }

    try {
      const validator = schema.validation[key];
      const isValid = validator(value, context.provider);

      return {
        valid: isValid,
        error: isValid ? null : `Invalid ${key} value: ${value}`
      };
    } catch (error) {
      return {
        valid: false,
        error: `Validation error for ${key}: ${error.message}`
      };
    }
  }

  static validateSection(section, config) {
    const schema = CONFIG_SCHEMA[section];
    if (!schema || !schema.validation) {
      return { valid: true, errors: [] };
    }

    const errors = [];
    for (const [key, value] of Object.entries(config)) {
      const result = this.validate(section, key, value, config);
      if (!result.valid) {
        errors.push(result.error);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static getDefaults(section) {
    const schema = CONFIG_SCHEMA[section];
    return schema ? { ...schema.defaults } : {};
  }
}

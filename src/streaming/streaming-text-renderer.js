// 流式文本渲染器
// 实现打字机效果的核心逻辑

export class StreamingTextRenderer {
  constructor(options = {}) {
    this.options = {
      speed: 50, // 字符每秒显示速度
      minDelay: 20, // 最小延迟（毫秒）
      maxDelay: 100, // 最大延迟（毫秒）
      punctuationDelay: 200, // 标点符号后的额外延迟
      enableCursor: true, // 是否显示光标
      cursorChar: '|', // 光标字符
      cursorBlinkSpeed: 500, // 光标闪烁速度（毫秒）
      ...options
    };

    this.isActive = false;
    this.isPaused = false;
    this.currentText = '';
    this.targetElement = null;
    this.cursorInterval = null;
    this.renderTimeout = null;
    this.abortController = null;

    // 回调函数
    this.onProgress = null;
    this.onComplete = null;
    this.onError = null;
  }

  // 开始流式渲染
  async startStreaming(targetElement, callbacks = {}) {
    if (this.isActive) {
      throw new Error('渲染器已在运行中');
    }

    this.targetElement = targetElement;
    this.onProgress = callbacks.onProgress;
    this.onComplete = callbacks.onComplete;
    this.onError = callbacks.onError;
    this.isActive = true;
    this.isPaused = false;
    this.currentText = '';
    this.abortController = new AbortController();

    // 清空目标元素
    this.targetElement.value = '';
    
    // 开始光标闪烁
    if (this.options.enableCursor) {
      this.startCursorBlink();
    }

    return this.abortController;
  }

  // 添加文本块
  async addTextChunk(chunk) {
    if (!this.isActive || this.isPaused) {
      return;
    }

    try {
      await this.renderTextChunk(chunk);
    } catch (error) {
      if (this.onError) {
        this.onError(error);
      }
    }
  }

  // 渲染文本块
  async renderTextChunk(chunk) {
    const chars = Array.from(chunk); // 支持Unicode字符
    
    for (const char of chars) {
      if (!this.isActive || this.abortController?.signal.aborted) {
        break;
      }

      // 等待暂停结束
      while (this.isPaused && this.isActive) {
        await this.sleep(100);
      }

      if (!this.isActive) break;

      // 添加字符到当前文本
      this.currentText += char;
      
      // 更新显示
      this.updateDisplay();

      // 触发进度回调
      if (this.onProgress) {
        this.onProgress(this.currentText, char);
      }

      // 计算延迟时间
      const delay = this.calculateDelay(char);
      await this.sleep(delay);
    }
  }

  // 完成渲染
  finishStreaming() {
    if (!this.isActive) return;

    this.isActive = false;
    this.isPaused = false;

    // 停止光标闪烁
    this.stopCursorBlink();

    // 清理定时器
    if (this.renderTimeout) {
      clearTimeout(this.renderTimeout);
      this.renderTimeout = null;
    }

    // 最终更新显示（移除光标）
    this.updateDisplay(false);

    // 触发完成回调
    if (this.onComplete) {
      this.onComplete(this.currentText);
    }
  }

  // 暂停渲染
  pause() {
    this.isPaused = true;
  }

  // 恢复渲染
  resume() {
    this.isPaused = false;
  }

  // 停止渲染
  stop() {
    this.isActive = false;
    this.isPaused = false;
    
    if (this.abortController) {
      this.abortController.abort();
    }
    
    this.stopCursorBlink();
    
    if (this.renderTimeout) {
      clearTimeout(this.renderTimeout);
      this.renderTimeout = null;
    }
  }

  // 跳过动画，直接显示完整内容
  skipAnimation(finalText) {
    this.stop();
    this.currentText = finalText;
    this.updateDisplay(false);
    
    if (this.onComplete) {
      this.onComplete(finalText);
    }
  }

  // 更新显示
  updateDisplay(showCursor = true) {
    if (!this.targetElement) return;

    let displayText = this.currentText;
    
    // 添加光标
    if (showCursor && this.options.enableCursor && this.isActive) {
      displayText += this.options.cursorChar;
    }

    // 更新元素内容
    if (this.targetElement.tagName === 'TEXTAREA' || this.targetElement.tagName === 'INPUT') {
      this.targetElement.value = displayText;
    } else {
      this.targetElement.textContent = displayText;
    }

    // 自动滚动到底部
    if (this.targetElement.scrollTop !== undefined) {
      this.targetElement.scrollTop = this.targetElement.scrollHeight;
    }
  }

  // 开始光标闪烁
  startCursorBlink() {
    if (this.cursorInterval) {
      clearInterval(this.cursorInterval);
    }

    let showCursor = true;
    this.cursorInterval = setInterval(() => {
      if (!this.isActive) {
        this.stopCursorBlink();
        return;
      }

      showCursor = !showCursor;
      this.updateDisplay(showCursor);
    }, this.options.cursorBlinkSpeed);
  }

  // 停止光标闪烁
  stopCursorBlink() {
    if (this.cursorInterval) {
      clearInterval(this.cursorInterval);
      this.cursorInterval = null;
    }
  }

  // 计算字符延迟时间
  calculateDelay(char) {
    const baseDelay = 1000 / this.options.speed;
    
    // 标点符号后增加延迟
    const punctuation = /[。！？，；：、""''（）【】《》]/;
    if (punctuation.test(char)) {
      return Math.max(baseDelay + this.options.punctuationDelay, this.options.minDelay);
    }
    
    // 添加随机变化使效果更自然
    const randomFactor = 0.5 + Math.random();
    const delay = baseDelay * randomFactor;
    
    return Math.max(Math.min(delay, this.options.maxDelay), this.options.minDelay);
  }

  // 睡眠函数
  sleep(ms) {
    return new Promise(resolve => {
      this.renderTimeout = setTimeout(resolve, ms);
    });
  }

  // 获取当前状态
  getStatus() {
    return {
      isActive: this.isActive,
      isPaused: this.isPaused,
      currentText: this.currentText,
      textLength: this.currentText.length
    };
  }

  // 设置选项
  setOptions(newOptions) {
    this.options = { ...this.options, ...newOptions };
  }

  // 清理资源
  cleanup() {
    this.stop();
    this.targetElement = null;
    this.onProgress = null;
    this.onComplete = null;
    this.onError = null;
  }
}

// 流式输出管理器
export class StreamingManager {
  constructor() {
    this.activeRenderers = new Map();
    this.globalOptions = {
      maxConcurrent: 1, // 最大并发数
      defaultSpeed: 50
    };
  }

  // 创建新的渲染器
  createRenderer(id, options = {}) {
    if (this.activeRenderers.has(id)) {
      this.activeRenderers.get(id).cleanup();
    }

    const renderer = new StreamingTextRenderer({
      speed: this.globalOptions.defaultSpeed,
      ...options
    });

    this.activeRenderers.set(id, renderer);
    return renderer;
  }

  // 获取渲染器
  getRenderer(id) {
    return this.activeRenderers.get(id);
  }

  // 停止所有渲染器
  stopAll() {
    for (const renderer of this.activeRenderers.values()) {
      renderer.stop();
    }
  }

  // 清理指定渲染器
  cleanup(id) {
    const renderer = this.activeRenderers.get(id);
    if (renderer) {
      renderer.cleanup();
      this.activeRenderers.delete(id);
    }
  }

  // 清理所有渲染器
  cleanupAll() {
    for (const [id, renderer] of this.activeRenderers) {
      renderer.cleanup();
    }
    this.activeRenderers.clear();
  }

  // 设置全局选项
  setGlobalOptions(options) {
    this.globalOptions = { ...this.globalOptions, ...options };
  }
}

// 导出单例管理器
export const streamingManager = new StreamingManager();

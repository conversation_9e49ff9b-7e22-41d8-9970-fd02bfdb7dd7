// 流式UI控制器
// 管理流式输出的UI交互和状态

import { streamingManager } from './streaming-text-renderer.js';

export class StreamingUIController {
  constructor(options = {}) {
    this.options = {
      showControls: true, // 是否显示控制按钮
      allowSkip: true, // 是否允许跳过动画
      showProgress: true, // 是否显示进度指示
      autoHideControls: true, // 完成后自动隐藏控制按钮
      ...options
    };

    this.currentRenderer = null;
    this.controlsContainer = null;
    this.progressIndicator = null;
    this.isStreaming = false;
    this.originalContent = '';
  }

  // 初始化UI控制器
  initialize(targetElement, controlsParent = null) {
    this.targetElement = targetElement;
    this.controlsParent = controlsParent || targetElement.parentElement;

    if (this.options.showControls) {
      this.createControlsUI();
    }

    if (this.options.showProgress) {
      this.createProgressIndicator();
    }
  }

  // 开始流式输出
  async startStreaming(rendererId, aiServiceCall, options = {}) {
    if (this.isStreaming) {
      throw new Error('已有流式输出在进行中');
    }

    this.isStreaming = true;
    this.originalContent = this.targetElement.value;

    // 创建渲染器
    this.currentRenderer = streamingManager.createRenderer(rendererId, {
      speed: 60, // 稍快的默认速度
      enableCursor: true,
      ...options
    });

    // 添加流式输出样式
    this.targetElement.classList.add('streaming');

    // 显示控制按钮和进度
    this.showControls();
    this.showProgress();

    // 更新进度文本
    this.updateProgressText('正在连接AI服务...');

    try {
      // 开始渲染
      const abortController = await this.currentRenderer.startStreaming(this.targetElement, {
        onProgress: (text, char) => {
          this.updateProgress(text.length);
          // 检测是否是标点符号，调整进度显示
          if (/[。！？，；：、""''（）【】《》]/.test(char)) {
            this.updateProgressText(`正在生成... (${text.length} 字符)`);
          }
        },
        onComplete: (finalText) => {
          this.onStreamingComplete(finalText);
        },
        onError: (error) => {
          this.onStreamingError(error);
        }
      });

      // 更新进度文本
      this.updateProgressText('正在生成内容...');

      // 调用AI服务
      await aiServiceCall({
        onChunk: (chunk) => {
          if (this.currentRenderer && this.currentRenderer.isActive) {
            this.currentRenderer.addTextChunk(chunk);
          }
        },
        onComplete: (result) => {
          if (this.currentRenderer) {
            this.currentRenderer.finishStreaming();
          }
        },
        onError: (error) => {
          this.onStreamingError(error);
        },
        signal: abortController.signal
      });

    } catch (error) {
      this.onStreamingError(error);
    }
  }

  // 流式输出完成
  onStreamingComplete(finalText) {
    this.isStreaming = false;

    // 移除流式输出样式
    this.targetElement.classList.remove('streaming');

    // 显示完成状态
    this.showCompletionStatus();

    // 添加完成动画效果
    this.targetElement.style.animation = 'slideIn 0.3s ease';
    setTimeout(() => {
      this.targetElement.style.animation = '';
    }, 300);

    // 自动隐藏控制按钮
    if (this.options.autoHideControls) {
      setTimeout(() => {
        this.hideControls();
      }, 3000); // 延长显示时间，让用户看到完成状态
    }
  }

  // 流式输出错误
  onStreamingError(error) {
    this.isStreaming = false;
    console.error('流式输出错误:', error);

    // 移除流式输出样式
    this.targetElement.classList.remove('streaming');

    // 显示错误状态
    this.showErrorStatus(this.getErrorMessage(error));

    // 恢复原始内容（给用户选择）
    this.showRecoveryOptions(error);

    // 清理渲染器
    if (this.currentRenderer) {
      this.currentRenderer.cleanup();
      this.currentRenderer = null;
    }
  }

  // 获取用户友好的错误消息
  getErrorMessage(error) {
    if (error.name === 'AbortError') {
      return '操作已取消';
    }

    if (error.message.includes('网络')) {
      return '网络连接失败，请检查网络设置';
    }

    if (error.message.includes('API')) {
      return 'AI服务暂时不可用，请稍后重试';
    }

    if (error.message.includes('配置')) {
      return 'AI配置有误，请检查设置';
    }

    return error.message || '未知错误';
  }

  // 显示恢复选项
  showRecoveryOptions(error) {
    if (!this.controlsContainer) return;

    // 清空现有控制按钮
    this.controlsContainer.innerHTML = '';

    // 恢复内容按钮
    const restoreBtn = document.createElement('button');
    restoreBtn.className = 'streaming-btn restore-btn';
    restoreBtn.innerHTML = '🔄 恢复原内容';
    restoreBtn.addEventListener('click', () => {
      if (this.originalContent) {
        this.targetElement.value = this.originalContent;
      }
      this.hideControls();
    });

    // 重试按钮
    const retryBtn = document.createElement('button');
    retryBtn.className = 'streaming-btn retry-btn';
    retryBtn.innerHTML = '🔁 重试';
    retryBtn.addEventListener('click', () => {
      // 这里需要重新触发原始操作
      // 由于我们不知道原始操作，所以只是隐藏控制按钮
      this.hideControls();
    });

    this.controlsContainer.appendChild(restoreBtn);
    this.controlsContainer.appendChild(retryBtn);
    this.showControls();

    // 5秒后自动隐藏
    setTimeout(() => {
      this.hideControls();
    }, 5000);
  }

  // 停止流式输出
  stopStreaming() {
    if (this.currentRenderer) {
      this.currentRenderer.stop();
    }
    this.isStreaming = false;
    this.hideControls();
    this.hideProgress();
  }

  // 暂停/恢复流式输出
  togglePause() {
    if (!this.currentRenderer) return;

    if (this.currentRenderer.isPaused) {
      this.currentRenderer.resume();
      this.updatePauseButton(false);
    } else {
      this.currentRenderer.pause();
      this.updatePauseButton(true);
    }
  }

  // 跳过动画
  skipAnimation() {
    if (!this.currentRenderer || !this.options.allowSkip) return;

    // 这里需要获取完整的最终文本
    // 由于我们不知道最终文本，我们停止动画并保持当前状态
    this.currentRenderer.stop();
    this.onStreamingComplete(this.currentRenderer.currentText);
  }

  // 创建控制按钮UI
  createControlsUI() {
    this.controlsContainer = document.createElement('div');
    this.controlsContainer.className = 'streaming-controls';
    this.controlsContainer.style.cssText = `
      display: none;
      margin-top: 8px;
      display: flex;
      gap: 8px;
      align-items: center;
    `;

    // 暂停/恢复按钮
    const pauseBtn = document.createElement('button');
    pauseBtn.className = 'streaming-btn pause-btn';
    pauseBtn.innerHTML = '⏸️ 暂停';
    pauseBtn.style.cssText = `
      padding: 4px 8px;
      font-size: 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: #f5f5f5;
      cursor: pointer;
      transition: all 0.2s;
    `;
    pauseBtn.addEventListener('click', () => this.togglePause());

    // 停止按钮
    const stopBtn = document.createElement('button');
    stopBtn.className = 'streaming-btn stop-btn';
    stopBtn.innerHTML = '⏹️ 停止';
    stopBtn.style.cssText = pauseBtn.style.cssText;
    stopBtn.addEventListener('click', () => this.stopStreaming());

    // 跳过按钮
    const skipBtn = document.createElement('button');
    skipBtn.className = 'streaming-btn skip-btn';
    skipBtn.innerHTML = '⏭️ 跳过';
    skipBtn.style.cssText = pauseBtn.style.cssText;
    skipBtn.addEventListener('click', () => this.skipAnimation());

    this.controlsContainer.appendChild(pauseBtn);
    this.controlsContainer.appendChild(stopBtn);

    if (this.options.allowSkip) {
      this.controlsContainer.appendChild(skipBtn);
    }

    this.controlsParent.appendChild(this.controlsContainer);
  }

  // 创建进度指示器
  createProgressIndicator() {
    this.progressIndicator = document.createElement('div');
    this.progressIndicator.className = 'streaming-progress';
    this.progressIndicator.style.cssText = `
      display: none;
      margin-top: 4px;
      font-size: 12px;
      color: #666;
      display: flex;
      align-items: center;
      gap: 8px;
    `;

    const spinner = document.createElement('div');
    spinner.className = 'streaming-spinner';
    spinner.style.cssText = `
      width: 12px;
      height: 12px;
      border: 2px solid #ddd;
      border-top: 2px solid #1a73e8;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    `;

    const text = document.createElement('span');
    text.className = 'progress-text';
    text.textContent = '正在生成...';

    this.progressIndicator.appendChild(spinner);
    this.progressIndicator.appendChild(text);
    this.controlsParent.appendChild(this.progressIndicator);

    // 添加旋转动画
    if (!document.querySelector('#streaming-animations')) {
      const style = document.createElement('style');
      style.id = 'streaming-animations';
      style.textContent = `
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .streaming-btn:hover {
          background: #e8eaed !important;
          border-color: #5f6368 !important;
        }
      `;
      document.head.appendChild(style);
    }
  }

  // 显示控制按钮
  showControls() {
    if (this.controlsContainer) {
      this.controlsContainer.style.display = 'flex';
    }
  }

  // 隐藏控制按钮
  hideControls() {
    if (this.controlsContainer) {
      this.controlsContainer.style.display = 'none';
    }
  }

  // 显示进度指示
  showProgress() {
    if (this.progressIndicator) {
      this.progressIndicator.style.display = 'flex';
    }
  }

  // 隐藏进度指示
  hideProgress() {
    if (this.progressIndicator) {
      this.progressIndicator.style.display = 'none';
    }
  }

  // 更新进度
  updateProgress(charCount) {
    if (this.progressIndicator) {
      const text = this.progressIndicator.querySelector('.progress-text');
      if (text) {
        text.textContent = `正在生成... (${charCount} 字符)`;
      }
    }
  }

  // 更新进度文本
  updateProgressText(message) {
    if (this.progressIndicator) {
      const text = this.progressIndicator.querySelector('.progress-text');
      if (text) {
        text.textContent = message;
      }
    }
  }

  // 设置输出速度
  setSpeed(speed) {
    if (this.currentRenderer) {
      this.currentRenderer.setOptions({ speed });
    }
  }

  // 获取当前状态
  getStatus() {
    return {
      isStreaming: this.isStreaming,
      hasRenderer: !!this.currentRenderer,
      rendererStatus: this.currentRenderer ? this.currentRenderer.getStatus() : null
    };
  }

  // 更新暂停按钮状态
  updatePauseButton(isPaused) {
    const pauseBtn = this.controlsContainer?.querySelector('.pause-btn');
    if (pauseBtn) {
      pauseBtn.innerHTML = isPaused ? '▶️ 继续' : '⏸️ 暂停';
    }
  }

  // 显示完成状态
  showCompletionStatus() {
    if (this.progressIndicator) {
      const text = this.progressIndicator.querySelector('.progress-text');
      const spinner = this.progressIndicator.querySelector('.streaming-spinner');

      if (text) text.textContent = '✅ 生成完成';
      if (spinner) spinner.style.display = 'none';

      this.progressIndicator.style.display = 'flex';

      setTimeout(() => {
        this.hideProgress();
      }, 2000);
    }
  }

  // 显示错误状态
  showErrorStatus(message) {
    if (this.progressIndicator) {
      const text = this.progressIndicator.querySelector('.progress-text');
      const spinner = this.progressIndicator.querySelector('.streaming-spinner');

      if (text) text.textContent = `❌ ${message}`;
      if (spinner) spinner.style.display = 'none';

      this.progressIndicator.style.display = 'flex';

      setTimeout(() => {
        this.hideProgress();
      }, 3000);
    }
  }

  // 清理资源
  cleanup() {
    this.stopStreaming();

    if (this.controlsContainer) {
      this.controlsContainer.remove();
    }

    if (this.progressIndicator) {
      this.progressIndicator.remove();
    }

    streamingManager.cleanupAll();
  }
}
